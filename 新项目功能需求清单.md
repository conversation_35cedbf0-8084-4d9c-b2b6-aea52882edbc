# 号池管理系统 - 功能需求清单

## 项目概述

基于对现有 qnb-pool 项目的深度分析，制定新项目的完整功能需求清单。新项目将采用 **React + Golang + MySQL/TiDB** 技术栈，实现一个现代化的号池管理系统。

## 技术架构

### 前端技术栈
- **框架**: React 18 + TypeScript + Vite
- **状态管理**: Zustand
- **UI 组件库**: Ant Design 5.x + Tailwind CSS
- **路由**: React Router v6
- **HTTP 客户端**: Axios + TanStack Query
- **表单处理**: React Hook Form + Zod

### 后端技术栈
- **语言**: Golang 1.21+
- **框架**: Gin
- **ORM**: GORM v2
- **认证**: JWT + 中间件
- **配置**: Viper
- **日志**: Logrus
- **数据验证**: go-playground/validator v10

### 数据库
- **主数据库**: MySQL 8.0+ / TiDB Cloud
- **兼容性**: 支持 MySQL 和 TiDB 双重兼容
- **连接池**: 优化配置，适配云数据库

## 核心功能模块

### 1. 用户认证与权限管理

#### 1.1 用户系统
- **管理员账户**
  - 管理员注册/登录
  - 会话管理（基于 Cookie）
  - 密码修改和重置
  - 管理员信息管理
  - 多因素认证（MFA）支持

- **普通用户账户**
  - 用户注册/登录
  - 用户信息管理
  - 用户状态管理（激活/禁用）
  - 用户配额管理

#### 1.2 API 密钥管理
- **密钥生成**
  - 自动生成唯一 API 密钥
  - 支持自定义密钥名称和描述
  - 密钥过期时间设置
  - 密钥权限范围配置

- **密钥管理**
  - 密钥列表查看
  - 密钥状态管理（激活/禁用）
  - 密钥删除和重新生成
  - 密钥使用统计和监控
  - 密钥访问日志记录

#### 1.3 权限控制
- **角色权限**
  - 管理员权限（完全访问）
  - 用户权限（受限访问）
  - 细粒度权限控制
  - 动态权限分配

- **API 权限**
  - 基于 API 密钥的认证
  - 权限范围配置
  - 访问频率限制（令牌桶算法）
  - IP 白名单控制
  - 请求签名验证

### 2. 号池类型管理

#### 2.1 号池类型
- **类型管理**
  - 创建号池类型
  - 编辑号池类型名称
  - 删除号池类型
  - 类型列表查看

- **类型统计**
  - 每个类型下的号池数量
  - 每个类型下的账号总数
  - 可用账号统计

### 3. 号池管理

#### 3.1 号池操作
- **基础操作**
  - 创建号池（指定类型）
  - 编辑号池信息
  - 删除号池
  - 号池列表查看

- **号池配置**
  - 号池名称和描述
  - 关联号池类型
  - 号池状态管理

#### 3.2 号池统计
- **实时统计**
  - 总账号数量
  - 可用账号数量
  - 占用中账号数量
  - 失效账号数量
  - 过期账号数量

- **使用率统计**
  - 号池使用率
  - 可用率
  - 最近使用情况

### 4. 账号管理

#### 4.1 账号操作
- **单个账号管理**
  - 添加账号到号池
  - 编辑账号内容
  - 设置账号过期时间
  - 添加账号备注
  - 删除账号

- **批量操作**
  - 批量导入账号（支持 CSV/JSON）
  - 批量更新账号状态
  - 批量设置过期时间
  - 批量删除账号

#### 4.2 账号状态管理
- **状态类型**
  - Available（可用）
  - InUse（占用中）
  - Invalid（失效）
  - Failed（失败）
  - Expired（已过期）
  - Warming（预热中）
  - Cooling（冷却中）
  - Retired（已退役）

- **状态转换**
  - 自动状态更新
  - 手动状态修改
  - 过期自动处理
  - 超时自动释放
  - 质量评估自动标记

#### 4.3 账号生命周期管理
- **超时释放机制**
  - 可配置的占用超时时间
  - 自动释放超时账号
  - 异常情况下的强制释放
  - 释放通知机制

- **质量评估系统**
  - 成功率统计
  - 连续失败检测
  - 自动质量分级
  - 低质量账号隔离

#### 4.4 账号搜索与筛选
- **搜索功能**
  - 按内容搜索（支持模糊匹配）
  - 按状态筛选
  - 按号池筛选
  - 按时间范围筛选
  - 按质量等级筛选

- **排序功能**
  - 按创建时间排序
  - 按最后使用时间排序
  - 按状态排序
  - 按质量评分排序

### 5. 核心 API 接口

#### 5.1 取号接口
- **GET /api/v1/account**
  - 支持按号池名称取号
  - 支持按号池类型取号
  - 原子化事务处理
  - 分布式锁保证并发安全
  - 多种取号策略（FIFO、随机、权重）
  - 质量优先取号选项
  - 取号失败时的降级策略

#### 5.2 归还接口
- **POST /api/v1/account/report**
  - 账号使用结果上报
  - 状态自动更新
  - 支持成功/失败标记
  - 清除占用标识
  - 质量评分更新
  - 使用统计记录

#### 5.3 统计接口
- **GET /api/v1/stats**
  - 实时统计数据
  - 支持按号池筛选
  - 支持按类型筛选
  - 多维度数据统计
  - 历史趋势数据
  - 质量分析报告

#### 5.4 健康检查
- **GET /api/v1/health**
  - 系统健康状态
  - 数据库连接检查
  - 响应时间统计
  - 基础统计信息
  - 依赖服务状态检查
  - 资源使用情况

### 6. 管理后台功能

#### 6.1 仪表板
- **系统概览**
  - 总体统计数据
  - 实时使用情况
  - 系统健康状态
  - 最近活动记录

- **数据可视化**
  - 使用率图表
  - 状态分布图
  - 趋势分析图
  - 性能监控图

#### 6.2 用户管理
- **用户列表**
  - 用户信息查看
  - 用户状态管理
  - 用户权限配置
  - 用户活动记录

- **API 密钥管理**
  - 全局密钥查看
  - 密钥权限配置
  - 密钥使用监控
  - 密钥安全管理

#### 6.3 系统监控
- **性能监控**
  - API 响应时间
  - 数据库性能
  - 系统资源使用
  - 错误率统计

- **日志管理**
  - 操作日志查看
  - 错误日志分析
  - 访问日志统计
  - 日志搜索功能

### 7. 数据库设计

#### 7.1 核心表结构
- **pool_types**: 号池类型表
- **pools**: 号池表
- **accounts**: 账号表
- **users**: 用户表
- **admins**: 管理员表
- **api_keys**: API 密钥表
- **user_sessions**: 用户会话表
- **admin_sessions**: 管理员会话表

#### 7.2 数据关系
- 号池类型 → 号池 (一对多)
- 号池 → 账号 (一对多)
- 用户 → API 密钥 (一对多)
- 用户 → 用户会话 (一对多)
- 管理员 → 管理员会话 (一对多)

### 8. 系统配置管理

#### 8.1 动态配置
- **业务配置**
  - 取号策略配置
  - 超时时间设置
  - 质量评估参数
  - 频率限制参数

- **系统配置**
  - 数据库连接配置
  - 缓存配置
  - 日志级别配置
  - 监控参数配置

#### 8.2 配置热更新
- 配置变更通知
- 无重启配置更新
- 配置版本管理
- 配置回滚机制

### 9. 审计日志系统

#### 9.1 操作审计
- **用户操作记录**
  - 登录/登出记录
  - 数据修改记录
  - 权限变更记录
  - 敏感操作记录

- **API 调用记录**
  - 取号操作记录
  - 归还操作记录
  - 管理操作记录
  - 异常操作记录

#### 9.2 审计查询
- 审计日志查询
- 操作轨迹追踪
- 异常行为分析
- 合规性报告生成

### 10. 通知中心

#### 10.1 实时通知
- **系统通知**
  - 系统状态变更通知
  - 资源不足告警
  - 异常情况通知
  - 维护计划通知

- **业务通知**
  - 账号质量告警
  - 配额使用告警
  - 操作结果通知
  - 统计报告推送

#### 10.2 通知渠道
- 站内消息
- 邮件通知
- 短信通知（可选）
- Webhook 回调

### 11. 数据导出与报表

#### 11.1 数据导出
- **账号数据导出**
  - 支持 CSV/Excel 格式
  - 自定义字段选择
  - 批量导出功能
  - 导出进度跟踪

- **统计数据导出**
  - 使用统计报表
  - 质量分析报表
  - 趋势分析报表
  - 自定义报表

#### 11.2 定时报表
- 日报/周报/月报
- 自动生成和发送
- 报表模板管理
- 报表订阅功能

### 12. 安全特性

#### 12.1 认证安全
- JWT Token 认证
- API 密钥认证
- 会话管理
- 密码加密存储
- 多因素认证（MFA）
- 单点登录（SSO）支持

#### 12.2 访问控制
- 基于角色的权限控制
- API 访问频率限制（令牌桶/滑动窗口）
- IP 白名单控制
- 请求签名验证
- 地理位置限制
- 设备指纹识别

#### 12.3 数据安全
- 敏感数据加密存储
- 传输数据加密（TLS）
- SQL 注入防护
- XSS 攻击防护
- CSRF 攻击防护
- 数据脱敏处理

#### 12.4 安全监控
- 异常登录检测
- 暴力破解防护
- 异常 API 调用监控
- 安全事件告警
- 入侵检测系统

### 13. 性能优化

#### 13.1 数据库优化
- 索引优化策略
- 查询优化和执行计划分析
- 连接池管理和调优
- 事务优化和死锁预防
- 分库分表策略（大数据量场景）
- 读写分离配置

#### 13.2 缓存策略
- Redis 分布式缓存
- 本地内存缓存
- 查询结果缓存
- 静态资源缓存
- 缓存一致性保证
- 缓存穿透/雪崩防护

#### 13.3 并发处理
- 分布式锁机制
- 原子化操作保证
- 事务隔离级别优化
- 消息队列异步处理
- 限流和熔断机制
- 负载均衡策略

#### 13.4 系统扩展性
- 水平扩展支持
- 微服务架构准备
- 服务发现和注册
- 配置中心集成
- 分布式事务处理

### 14. 异常处理与容灾

#### 14.1 异常处理机制
- **网络异常处理**
  - 连接超时重试
  - 断线重连机制
  - 网络分区处理
  - 降级服务策略

- **数据异常处理**
  - 数据校验和修复
  - 数据不一致检测
  - 脏数据清理
  - 数据恢复机制

#### 14.2 容灾备份
- **数据备份策略**
  - 定时全量备份
  - 增量备份机制
  - 跨地域备份
  - 备份数据验证

- **灾难恢复方案**
  - RTO/RPO 目标设定
  - 故障切换机制
  - 数据恢复流程
  - 业务连续性保证

### 15. 部署与运维

#### 15.1 容器化部署
- Docker 镜像构建优化
- Docker Compose 多环境配置
- Kubernetes 部署支持
- 环境变量安全管理
- 健康检查和就绪探针

#### 15.2 CI/CD 流水线
- 自动化构建和测试
- 多环境部署流水线
- 蓝绿部署/金丝雀发布
- 自动回滚机制
- 部署审批流程

#### 15.3 监控告警增强
- **系统监控**
  - CPU、内存、磁盘监控
  - 网络流量监控
  - 服务可用性监控
  - 依赖服务监控

- **业务监控**
  - 取号成功率监控
  - 账号质量趋势监控
  - 用户行为分析
  - 业务指标告警

- **告警机制**
  - 多级告警策略
  - 告警收敛和去重
  - 告警升级机制
  - 告警恢复通知

## 非功能性需求

### 性能要求
- **响应时间**
  - API 响应时间 < 200ms（P95）
  - 核心取号接口 < 100ms（P99）
  - 页面首屏加载 < 2s
  - 静态资源加载 < 1s

- **并发能力**
  - 支持 5000+ 并发请求
  - 单机支持 1000+ QPS
  - 数据库连接池 100+ 连接
  - 缓存命中率 > 90%

- **吞吐量**
  - 日处理请求量 > 1000万
  - 峰值 QPS > 10000
  - 数据库 TPS > 5000

### 可用性要求
- **系统可用性**
  - 系统可用性 99.95%（年停机时间 < 4.4小时）
  - 故障恢复时间 < 3min（MTTR）
  - 故障检测时间 < 30s
  - 服务降级响应时间 < 5s

- **数据可靠性**
  - 数据持久性 99.999%
  - 数据备份成功率 100%
  - 备份恢复时间 < 30min
  - 数据一致性检查通过率 100%

### 扩展性要求
- **水平扩展**
  - 支持无状态服务扩展
  - 数据库读写分离
  - 缓存集群支持
  - 负载均衡自动扩缩容

- **垂直扩展**
  - 支持单机性能提升
  - 资源利用率优化
  - 内存和 CPU 弹性调整

### 安全性要求
- **数据安全**
  - 数据传输 TLS 1.3 加密
  - 敏感数据 AES-256 加密存储
  - 数据脱敏处理
  - 数据访问审计 100% 覆盖

- **访问安全**
  - 身份认证成功率 > 99.9%
  - 异常访问检测准确率 > 95%
  - 安全事件响应时间 < 5min
  - 密码策略强度检查

### 兼容性要求
- **浏览器兼容**
  - Chrome 90+
  - Firefox 88+
  - Safari 14+
  - Edge 90+

- **数据库兼容**
  - MySQL 8.0+
  - TiDB 5.0+
  - 无缝切换支持

### 运维要求
- **监控覆盖**
  - 系统监控覆盖率 100%
  - 业务监控覆盖率 > 95%
  - 告警响应时间 < 1min
  - 误报率 < 5%

- **部署要求**
  - 自动化部署成功率 > 99%
  - 部署时间 < 10min
  - 回滚时间 < 5min
  - 零停机部署支持

## 开发计划

### 第一阶段：基础架构（2-3周）
- 后端项目搭建
- 数据库设计和迁移
- 基础认证系统
- 核心 API 接口

### 第二阶段：核心功能（3-4周）
- 号池管理功能
- 账号管理功能
- 取号/归还逻辑
- 统计功能实现

### 第三阶段：前端开发（4-5周）
- React 项目搭建
- UI 组件库开发
- 页面功能实现
- 前后端集成

### 第四阶段：测试优化（2-3周）
- 单元测试
- 集成测试
- 性能测试
- 安全测试

### 第五阶段：部署上线（1-2周）
- 生产环境配置
- CI/CD 流水线
- 监控告警配置
- 文档完善

## 技术风险评估与应对策略

### 高风险项
- **并发取号的原子性保证**
  - 风险：高并发下数据竞争导致重复取号
  - 应对：分布式锁 + 数据库事务 + 乐观锁机制
  - 验证：压力测试 + 并发安全测试

- **数据库事务一致性**
  - 风险：分布式环境下数据不一致
  - 应对：两阶段提交 + 补偿事务 + 最终一致性
  - 验证：事务测试 + 数据一致性检查

- **高并发性能优化**
  - 风险：系统在高负载下性能急剧下降
  - 应对：缓存策略 + 读写分离 + 限流熔断
  - 验证：性能测试 + 容量规划

- **数据安全和隐私保护**
  - 风险：敏感数据泄露或被恶意访问
  - 应对：数据加密 + 访问控制 + 审计日志
  - 验证：安全测试 + 渗透测试

### 中风险项
- **系统扩展性设计**
  - 风险：系统无法支持业务快速增长
  - 应对：微服务架构 + 水平扩展 + 弹性伸缩
  - 验证：扩展性测试 + 架构评审

- **第三方依赖管理**
  - 风险：依赖服务不可用影响系统稳定性
  - 应对：服务降级 + 熔断机制 + 多活部署
  - 验证：故障演练 + 依赖测试

- **数据迁移和兼容性**
  - 风险：数据迁移失败或兼容性问题
  - 应对：分步迁移 + 数据校验 + 回滚方案
  - 验证：迁移测试 + 兼容性测试

### 低风险项
- **UI 界面开发**
  - 风险：界面不符合用户体验要求
  - 应对：原型设计 + 用户测试 + 迭代优化
  - 验证：用户体验测试

- **基础 CRUD 操作**
  - 风险：基础功能实现错误
  - 应对：单元测试 + 集成测试 + 代码审查
  - 验证：功能测试

- **日志监控功能**
  - 风险：监控不全面或告警不及时
  - 应对：监控指标梳理 + 告警策略优化
  - 验证：监控测试 + 告警测试

### 风险缓解措施
- **技术预研**：对高风险技术点进行预研和 POC 验证
- **分阶段实施**：将高风险功能分阶段实施，降低整体风险
- **备选方案**：为关键技术点准备备选实现方案
- **专家咨询**：邀请技术专家进行架构评审和风险评估
- **持续监控**：建立风险监控机制，及时发现和处理风险

## 质量保证体系

### 测试策略
- **单元测试**：覆盖率 > 80%，关键业务逻辑 > 95%
- **集成测试**：API 接口测试覆盖率 > 90%
- **端到端测试**：核心业务流程 100% 覆盖
- **性能测试**：压力测试、负载测试、稳定性测试
- **安全测试**：渗透测试、漏洞扫描、安全审计
- **兼容性测试**：多浏览器、多数据库、多环境测试

### 代码质量
- **代码规范**：统一的编码规范和代码风格
- **代码审查**：所有代码变更必须经过 Code Review
- **静态分析**：使用工具进行代码质量检查
- **技术债务管理**：定期评估和清理技术债务

### 文档体系
- **API 文档**：完整的 API 接口文档和示例
- **架构文档**：系统架构设计和技术选型说明
- **部署文档**：详细的部署和运维指南
- **用户手册**：面向最终用户的使用指南
- **开发指南**：面向开发者的开发规范和最佳实践

## 项目里程碑

### 里程碑 1：基础架构完成（第 4 周）
- 后端基础框架搭建完成
- 数据库设计和基础数据模型实现
- 基础认证和权限系统实现
- 核心 API 接口框架搭建

### 里程碑 2：核心功能实现（第 8 周）
- 号池和账号管理功能完成
- 取号和归还核心逻辑实现
- 基础统计和监控功能完成
- 前端基础框架和 UI 组件库完成

### 里程碑 3：完整功能交付（第 12 周）
- 所有业务功能开发完成
- 前后端完整集成
- 基础测试完成
- 部署环境搭建完成

### 里程碑 4：生产就绪（第 16 周）
- 完整测试体系验证通过
- 性能优化和安全加固完成
- 监控告警系统完善
- 文档体系完整

### 里程碑 5：正式上线（第 18 周）
- 生产环境部署完成
- 用户培训和支持体系建立
- 运维流程和应急预案完善
- 项目正式交付上线

## 成功标准

### 功能完整性
- 所有需求功能 100% 实现
- 核心业务流程验证通过
- 用户验收测试通过

### 性能指标
- 满足所有非功能性需求指标
- 性能测试基准达标
- 系统稳定性验证通过

### 质量标准
- 代码质量检查通过
- 安全测试无高危漏洞
- 文档完整性检查通过

### 用户满意度
- 用户体验评分 > 4.5/5
- 系统易用性评分 > 4.0/5
- 功能完整性评分 > 4.5/5

## 总结

本功能需求清单基于对现有 qnb-pool 项目的深入分析和辩证思考，结合现代化技术栈的优势，设计了一个完整、安全、高性能的号池管理系统。

### 核心优势
1. **完整性**：覆盖了号池管理的全生命周期，从账号导入到质量管理
2. **安全性**：多层次的安全防护，从认证授权到数据加密
3. **可靠性**：完善的异常处理和容灾机制，保证系统稳定运行
4. **扩展性**：支持水平扩展和微服务架构，适应业务增长
5. **易用性**：直观的用户界面和完善的 API 文档

### 技术亮点
- 分布式锁保证并发安全
- 多种取号策略满足不同场景需求
- 智能质量评估系统提升账号可用性
- 完善的监控告警体系保证系统健康
- 灵活的配置管理支持动态调整

新项目将在保持核心功能完整性的基础上，提供更好的性能、安全性和可维护性，为用户提供优质的号池管理服务。
